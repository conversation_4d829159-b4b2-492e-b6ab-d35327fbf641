#!/bin/bash

SERVER_PORT=60000
IMAGE_NAME="registry.earthos.cn/ai/python:3.12"
USERNAME="robot\$image"
PASSWORD="rCICBUc3BxOpOzpIq0Oiq7CVBRNR1zDn"
WORK_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/." && pwd)"
CONFIG_FILE="$WORK_DIR/Examples/config.json"

################################## Functions Start #################################################

get_ipv4_address() {
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
        # Windows（Git Bash 或 WSL）
        ipconfig | awk '/IPv4 Address/ {gsub(/\r/, "", $NF); print $NF; exit}'
    elif command -v ip >/dev/null; then
        # Linux
        ip -4 route get ******* 2>/dev/null | awk '{print $7; exit}'
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        ipconfig getifaddr en0 2>/dev/null || ipconfig getifaddr en1 2>/dev/null
    elif command -v ifconfig >/dev/null; then
        # Fallback for older Linux/mac
        ifconfig | awk '/inet / && $2 != "127.0.0.1" {print $2; exit}'
    else
        echo "Unable to determine IP address"
    fi
}

init_docker_image() {
    # Check if the image exists locally
    if ! docker image inspect "$IMAGE_NAME" &> /dev/null; then
        echo "Image $IMAGE_NAME not found locally. Pulling from registry..."
        
        # Log in to the Docker registry
        echo "$PASSWORD" | docker login -u "$USERNAME" --password-stdin registry.earthos.cn
        
        # Pull the image
        docker pull "$IMAGE_NAME"
        
        # Check if the pull was successful
        if [ $? -eq 0 ]; then
            echo "Image $IMAGE_NAME pulled successfully."
        else
            echo "Failed to pull image $IMAGE_NAME."
            exit 1
        fi
    else
        echo "Image $IMAGE_NAME already exists locally."
    fi
}


run_docker_container() {

    HOST_IP=$(get_ipv4_address)
    # 提取 flask启动的 port
    APP_PORT=$(grep '"port"' "$CONFIG_FILE" | head -1 | sed 's/.*: *\([0-9]*\),*/\1/')
    APP_PORT=$(echo "$APP_PORT" | tr -d '\r')
    set -x
    docker run -d --gpus all \
        -v "$WORK_DIR":/work -p "$SERVER_PORT":"$APP_PORT" \
        -e SERVER_PORT="$SERVER_PORT" \
        -e HOST_IP="$HOST_IP" \
        "$IMAGE_NAME" /bin/bash /work/run.sh
}
################################## Functions End #################################################


### start ###
init_docker_image \
&& run_docker_container
