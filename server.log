HOST_IP 环境变量未设置
SERVER_PORT 环境变量未设置
HOST_IP already set in /BaseOS/.env, skipping overwrite.
SERVER_PORT already set in /BaseOS/.env, skipping overwrite.
Processing ./baseos_ai_framework-0.1.0-py3-none-any.whl
baseos-ai-framework is already installed with the same version as the provided wheel. Use --force-reinstall to force an installation of the wheel.
BASEOSNODEID already set in /BaseOS/.env, skipping overwrite.
[2025-09-09 16:57:13 +0800] [822] [INFO] Starting gunicorn 23.0.0
[2025-09-09 16:57:13 +0800] [822] [ERROR] Connection in use: ('0.0.0.0', 5000)
[2025-09-09 16:57:13 +0800] [822] [ERROR] connection to ('0.0.0.0', 5000) failed: [Errno 98] Address already in use
[2025-09-09 16:57:14 +0800] [822] [ERROR] Connection in use: ('0.0.0.0', 5000)
[2025-09-09 16:57:14 +0800] [822] [ERROR] connection to ('0.0.0.0', 5000) failed: [Errno 98] Address already in use
[2025-09-09 16:57:15 +0800] [822] [ERROR] Connection in use: ('0.0.0.0', 5000)
[2025-09-09 16:57:15 +0800] [822] [ERROR] connection to ('0.0.0.0', 5000) failed: [Errno 98] Address already in use
[2025-09-09 16:57:16 +0800] [822] [ERROR] Connection in use: ('0.0.0.0', 5000)
[2025-09-09 16:57:16 +0800] [822] [ERROR] connection to ('0.0.0.0', 5000) failed: [Errno 98] Address already in use
[2025-09-09 16:57:17 +0800] [822] [ERROR] Connection in use: ('0.0.0.0', 5000)
[2025-09-09 16:57:17 +0800] [822] [ERROR] connection to ('0.0.0.0', 5000) failed: [Errno 98] Address already in use
[2025-09-09 16:57:18 +0800] [822] [ERROR] Can't connect to ('0.0.0.0', 5000)
