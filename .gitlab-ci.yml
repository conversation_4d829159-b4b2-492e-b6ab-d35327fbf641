# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

stages:          # List of stages for jobs, and their order of execution
  - build
  - test

build-job:       # This job runs in the build stage, which runs first.
  stage: build
  image:
    name: registry.earthos.cn/ai/python:3.12
    entrypoint: [""]

  script:
    - echo "$CI_PROJECT_DIR"
    - pip install build -i https://mirrors.aliyun.com/pypi/simple/
    - ls $CI_PROJECT_DIR
    - cd $CI_PROJECT_DIR/ai_service_framework
    - bash build.sh
  artifacts:
    paths:
      - ai_service_framework/baseos_ai_framework-0.1.0-py3-none-any.whl
  cache:
    paths:
      - ai_service_framework/baseos_ai_framework-0.1.0-py3-none-any.whl

test-job:
  stage: test
  image:
    name: registry.earthos.cn/ai/python:3.12
    entrypoint: [""]
  script:
    - echo "$CI_PROJECT_DIR"
    - cd $CI_PROJECT_DIR
    - cp ai_service_framework/baseos_ai_framework-0.1.0-py3-none-any.whl ExampleProject/
    - bash ci_test.sh
  artifacts:
    paths:
      - ExampleProject
