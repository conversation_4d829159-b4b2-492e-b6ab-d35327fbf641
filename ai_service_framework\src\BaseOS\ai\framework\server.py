import os
import json
import uuid
import time
from datetime import datetime, timezone
from abc import ABC
from functools import partial

from flask import Flask, Response, g, request, render_template
import requests

from .service import ModelService
from .utils import get_logger

from .utils.register_utils import register_2_agent, register_2_client
from .config import CONFIG, set_register_config, init_urls, reset_config

logger = get_logger()


class Server(ABC):
    """
    提供server的各种方法
    """

    def __init__(self, config=None, upload_dir=None):
        with open(config, 'r', encoding='utf-8') as file:
            self.config = json.load(file)
        reset_config()
        set_register_config(self.config)
        init_urls()
        self.services = dict()
        self.upload_dir = upload_dir
        self.register = self.config.get("register", None)
        self._proxy_process = None
        self.get_url_dict()
        if self.register:
            self.start_proxy_process()
            time.sleep(5)  # 等待代理进程启动

        # 从环境变量中获取这些值，用来注册给agent的
        self.host_ip = os.environ.get("HOST_IP", "127.0.0.1")  # TODO
        self.server_port = os.environ.get("SERVER_PORT", 5000)
        self.baseos_node_id = os.environ.get(
            "BASEOSNODEID", "default_baseos_id")
        self.create_app()

    # TODO 精简
    def get_url_dict(self):
        """
        获取服务的URL字典
        :return: 包含服务名称和对应URL的字典
        """
        self.url2service = dict()
        for service_name, svconfig in self.config["services"].items():
            svurl = svconfig.get("url", '/{}/predict'.format(service_name))
            self.url2service[svurl] = service_name

    def create_app(self) -> Flask:
        """
        Factory to create Flask app with injected service implementation.
        """
        template_path = os.path.join(os.path.dirname(__file__), 'templates')
        app = Flask(__name__, template_folder=template_path)

        @app.before_request
        def inject_meta():
            """
            为每个请求注入 CloudEvents 风格的元信息到 flask.g.meta, 并封装原始请求数据
            """
            # 获取原始请求数据（JSON）以便后续使用
            raw_data = None
            try:
                raw_data = request.get_json(force=True)
            except Exception:
                raw_data = None

            path_parts = request.path.split(os.sep)
            service = None

            # 处理根路径
            if request.path == '/':
                type_info = 'root'

            elif len(path_parts) > 1 and path_parts[1] == 'info':
                type_info = 'info'

            elif len(path_parts) > 1 and path_parts[1] in CONFIG["services"].keys():
                service = path_parts[1]
                info = CONFIG["services"][service]
                type_info = '.'.join(
                    [info["type"], info["subtype"], info["name"]])

            else:
                service = self.url2service.get(request.path, None)
                if service is None:
                    logger.error(
                        "Service not found for path: {}".format(request.path))
                    return Response(
                        json.dumps({"error": "Service not found"}),
                        status=404
                    )

            g.meta = {
                # req: 唯一事件ID
                "id": str(uuid.uuid4()),
                # opt: 版本
                "specversion": "1.0",
                # req: 事件类型
                "type": type_info,
                # req: 事件来源
                "source": request.path,
                # opt: 事件来源详情，可根据需要自定义
                "subject": request.headers.get("X-Subject", ""),
                # opt: 请求时间戳
                "time": datetime.now(timezone.utc).isoformat(),
                # req: 数据内容类型
                "datacontenttype": request.content_type or "application/json",
                # req: 原始请求数据
                "data": raw_data
            }

        info_view = partial(self.get_info)
        info_view.__name__ = 'info'
        app.add_url_rule('/info', view_func=info_view, methods=['GET'])

        # 绑定根路径，返回info.html模板
        root_view = partial(self.get_root_info)
        root_view.__name__ = 'root'
        app.add_url_rule('/', view_func=root_view, methods=['GET'])

        # 初始化根路径信息的基本数据（一次性计算）
        self._init_root_info_base_data()

        self.app = app

    def get_info(self):
        """
        获取服务的基本信息
        :return: 包含服务名称、端口和URL的字典
        """
        sv2url = dict()
        # 遍历服务配置
        for service_name, svconfig in self.config["services"].items():
            url = svconfig.get("url", f'/{service_name}/predict')
            sv2url[service_name] = url
        return sv2url

    def _init_root_info_base_data(self):
        """
        初始化根路径信息的基本数据（一次性计算）
        """
        # 获取代理地址
        proxy_address = ""
        if self.register:
            # 如果启用了代理，获取代理客户端返回的代理地址
            try:
                client_url = "http://localhost:{0}/url/register".format(
                    self.register.get("proxy_client_port", 8090))
                response = requests.post(
                    client_url,
                    json={
                        "app_name": CONFIG["app_name"],
                        "service_address": str(CONFIG["app_port"]),
                        "service_name": "root",
                        "service_group": "default",
                        "base_url": "/",
                        "api_type": "Agent"
                    },
                    timeout=10)
                if response.status_code == 200:
                    proxy_data = response.json()
                    # 获取代理地址信息
                    if proxy_data:
                        proxy_address = proxy_data.get("url_path", "")
            except Exception as e:
                logger.warning("Failed to get proxy address: {}".format(e))
        # 构建主节点地址
        main_node_address = "http://*************:8888"
        if proxy_address:
            main_node_address += proxy_address

        # 保存基本数据
        self._root_info_base_data = {
            'nodeid': self.baseos_node_id,
            'hostname': CONFIG["app_name"],
            'status': 'online',
            'ip': self.host_ip,
            'mainNodeAddress': main_node_address
        }

    def get_root_info(self):
        """
        获取根路径信息，返回info.html模板
        """
        # 使用预先计算的基本数据
        template_data = self._root_info_base_data.copy()
        template_data['items'] = []
        # 重新获取服务列表信息（每次都需要更新）
        for _, service_info in CONFIG["services"].items():
            item = {
                'name': service_info['name'],
                'description': service_info['name_zh'],
                'status': 'Running',  # 状态均为在线，对应前端的Running状态
                'servicePath': service_info["proxy_url_path"] + service_info['url'],  # 服务前缀
                'serviceBaseUri': '',  # 服务地址和端口号隐藏，设为空
                'port': ''  # 端口号隐藏，设为空
            }
            template_data['items'].append(item)

        return render_template('info.html', node_data=json.dumps(template_data))

    def create_services_from_config(self):
        self.name_to_service = dict()
        self.service_to_name = dict()
        # 收集服务信息用于info页面显示
        try:
            for _, v in CONFIG["services"].items():
                sv, svname = ModelService.create_service(v, self.upload_dir)
                self.name_to_service[svname] = sv
                self.service_to_name[sv] = svname

        except Exception as e:
            logger.error("Error creating services: {}".format(e))

    def bind_sv_urls(self):

        for _, v in CONFIG["services"].items():
            svname = v["name"]
            sv = self.name_to_service[svname]
            url_prefix = '/{}'.format(svname)
            url = v.get("url")

            # 使用 partial 包装实例方法
            predict_view = partial(sv.predict, request)
            health_view = partial(sv.health_check)
            demo_view = partial(sv.demo)
            metadata_view = partial(sv.metadata)
            homepage_view = partial(sv.homepage)

            # 更新视图函数的 __name__ 属性
            predict_view.__name__ = svname + 'predict'
            health_view.__name__ = svname + 'health'
            demo_view.__name__ = svname + 'demo'
            metadata_view.__name__ = svname + 'metadata'
            homepage_view.__name__ = svname + 'home'

            # 注册路由
            self.app.add_url_rule(
                '{}/healthcheck'.format(url_prefix), view_func=health_view, methods=['GET'])
            self.app.add_url_rule(
                '{}/demo'.format(url_prefix), view_func=demo_view)
            self.app.add_url_rule(
                '{}/metadata'.format(url_prefix), view_func=metadata_view, methods=['GET'])
            self.app.add_url_rule(
                '{}/home'.format(url_prefix), view_func=homepage_view)
            # 处理预测请求，位置需要放到绑定路由的最后，因为支持通配符，用户可能会绑定成 / ，这样的话会导致其他路由无法访问
            self.app.add_url_rule(
                url, view_func=predict_view, methods=['POST', 'GET'])
            # TODO
            self.app.add_url_rule(
                '{}/<path:subpath>'.format(url), view_func=predict_view, methods=['POST', 'GET'])

            logger.info("Service {} bound to URL {}".format(svname, url))
            logger.info(
                "Service {} bound to URL {}/healthcheck".format(svname, url_prefix))
            logger.info(
                "Service {} bound to URL {}/demo".format(svname, url_prefix))
            logger.info(
                "Service {} bound to URL {}/metadata".format(svname, url_prefix))
            logger.info(
                "Service {} bound to URL {}/home".format(svname, url_prefix))
            # 在client中注册
            if self.register:
                register_2_client(svname)

        # 只注册一次到agent
        if self.register and self.register.get("agent_register_url", None) and self.register.get("agent_heartbeat_url", None):
            register_2_agent(self.host_ip, self.server_port,
                             self.baseos_node_id)
            self._start_heartbeat(CONFIG["agent_heartbeat_url"], self.baseos_node_id + "_" + CONFIG["app_name"])

    def remove_file(filepath):
        try:
            os.remove(filepath)
            logger.info("file {} removed".format(filepath))
        except FileNotFoundError:
            logger.error("file {} not exits".format(filepath))
        except PermissionError:
            logger.error("{} permission denied".format(filepath))
        except Exception as e:
            logger.error("remove failed: {}".format(e))

    def start_proxy_process(self):
        """
        启动 TunnelGatewayClient 代理进程，参数从配置文件 register 字段读取
        """
        import platform
        import subprocess
        import threading
        from pathlib import Path

        proxy_client_name = CONFIG["proxy_client_name"]
        proxy_client_port = CONFIG["proxy_client_port"]
        proxy_server_ip = CONFIG["proxy_server_ip"]
        proxy_server_port = CONFIG["proxy_server_port"]

        # 获取包安装路径
        package_path = Path(__file__).parent.parent
        proxy_path = package_path / "proxy"

        # 根据操作系统选择可执行文件
        system = platform.system().lower()
        if system == "windows":
            executable = proxy_path / "BaseOS.Desktop.TunnelGateway.exe"

        else:
            executable = proxy_path / "BaseOS.Desktop.TunnelGateway"

        os.chmod(executable, 0o755)
        if not executable.exists():
            logger.error("Proxy executable not found: {}".format(executable))
            return

        # 拼接命令行参数
        cmd = [
            str(executable),
            "-server", str(proxy_server_ip),
            "-port", str(proxy_server_port),
            "-host", "127.0.0.1",
            "-manager", str(proxy_client_port),
            "-type", "AI",
            "-name", str(proxy_client_name),
            "-group", str(self.register.get("proxy_client_group",
                          "DefaultGroup"))
        ]

        def log_output(pipe, prefix):
            """读取进程输出并写入日志"""
            try:
                for line in iter(pipe.readline, ''):
                    if line:
                        # 移除行尾的换行符并记录到日志
                        logger.info("[{}] {}".format(prefix, line.rstrip()))
            except Exception as e:
                logger.error("Error reading {} output: {}".format(prefix, e))
            finally:
                pipe.close()

        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,  # 行缓冲
                universal_newlines=True
            )

            # 启动线程来处理stdout和stderr输出
            stdout_thread = threading.Thread(
                target=log_output,
                args=(process.stdout, "PROXY"),
                daemon=True
            )
            stderr_thread = threading.Thread(
                target=log_output,
                args=(process.stderr, "PROXY"),
                daemon=True
            )

            stdout_thread.start()
            stderr_thread.start()

            logger.info(
                "Started proxy process with PID: {}, cmd: {}".format(process.pid, ' '.join(cmd)))
            self._proxy_process = process
        except Exception as e:
            logger.error("Failed to start proxy process: {}".format(e))

    def _start_heartbeat(self, agent_heatbeat_url, serverName):
        """
        启动心跳检测协程
        """
        import asyncio
        import threading

        def heartbeat():
            try:
                requests.post(
                    agent_heatbeat_url,
                    params={
                        "serverName": serverName
                    }
                )
            except Exception as e:
                logger.error("Heartbeat error: {}".format(str(e)))

        async def heartbeat_loop():
            while True:
                try:
                    heartbeat()
                    await asyncio.sleep(60)
                except Exception as e:
                    logger.error("Heartbeat error: {}".format(str(e)))
                    await asyncio.sleep(5)

        def run_async_loop():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self._heartbeat_task = loop.create_task(heartbeat_loop())
            loop.run_forever()

        thread = threading.Thread(target=run_async_loop, daemon=True)
        thread.start()
        logger.info("Heartbeat service started")

    def __del__(self):
        """
        确保在对象销毁时清理代理进程
        """
        if hasattr(self, '_proxy_process') and self._proxy_process:
            try:
                self._proxy_process.terminate()
                self._proxy_process.wait(timeout=5)  # 等待进程结束
            except Exception as e:
                logger.error("Error terminating proxy process: {}".format(e))

        if hasattr(self, '_heartbeat_task') and self._heartbeat_task:
            self._heartbeat_task.cancel()
