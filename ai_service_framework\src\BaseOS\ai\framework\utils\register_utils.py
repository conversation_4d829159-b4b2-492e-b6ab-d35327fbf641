import requests
from . import get_logger

from ..config import CONFIG
from pathlib import Path

logger = get_logger()


def remove_first_level_pathlib(path):
    p = Path(path)
    if len(p.parts) > 1:
        return Path('/').joinpath(*p.parts[2:])  # 绝对路径跳过根目录和第一级
    return p.name


def is_url_supported(url):
    """
    检查URL是否在支持的列表中
    """
    supported_urls = ["/embeddings",
                      "/chat/completions", "/audio/transcriptions"]
    
    for su in supported_urls:
        if su in url:
            return su
    
    return None

# 全局注册信息（可按需扩展）


def register_2_agent(host_ip, server_port, baseos_node_id):
    """
    批量将服务注册到远程Agent
    """
    routers = []
    for _, svinfo in CONFIG["services"].items():
        # 去掉拼接的service name
        format_url = is_url_supported(svinfo["url"])
        # TODO 已经注册给client的地址才注册给agent
        if "proxy_url_path" not in svinfo or not svinfo["proxy_url_path"] or not format_url:
            logger.warning(
                "Service {} is not supported for registration to Agent.".format(svinfo["name"]))
            continue
        # TODO 去掉中间
        router_info = {
            "router": format_url,
            "type": svinfo["type"],
            "address": "{0}{1}".format(svinfo["proxy_url_path"], svinfo["url"]),
            "models": svinfo["parameters"].get("models", ["default"])
        }
        routers.append(router_info)
    # TODO
    payload = {
        # agent唯一key
        "serverName": CONFIG["proxy_client_name"] + "_" + CONFIG["app_name"],
        "aliasName": CONFIG["app_name"],  # TODO
        "routers": routers,
        "groupName": CONFIG["proxy_client_group"],
        "appName": CONFIG["app_name"],
        "clientName": CONFIG["proxy_client_name"]
    }
    logger.info("request agent register body: {}".format(payload))
    logger.info("service ip: {}, port: {}, id: {}".format(
        host_ip, server_port, baseos_node_id))
    if CONFIG["agent_register_url"] == "" or CONFIG["agent_heartbeat_url"] == "":
        logger.warning(
            "Agent registration URLs are not provided, skipping registration.")
        return
    if not routers:
        logger.error("No routers to register to Agent.")
        return

    try:
        headers = {'Content-Type': 'application/json'}
        response = requests.post(
            CONFIG["agent_register_url"], json=payload, headers=headers)
        if response.status_code == 200:
            logger.info("Services registered successfully")
        else:
            logger.error("Registration failed with status code: {}".format(
                response.status_code))
            logger.error("Response: {}".format(response.text))
    except requests.exceptions.RequestException as e:
        logger.error("Registration request failed: {}".format(str(e)))
    except Exception as e:
        logger.error("Registration error: {}".format(str(e)))


def register_2_client(service_name):
    """
    将服务注册到本地Client
    """
    svinfo = CONFIG["services"].get(service_name, {})

    if not svinfo:
        logger.error(f"Service {service_name} not found in configuration.")
        return

    p = {
        "app_name": CONFIG["app_name"],
        "service_address": str(CONFIG["app_port"]),
        "service_name": service_name,
        "service_group": svinfo.get("group", "default"),
        "base_url": svinfo.get("url"),
        "api_type": get_tunnel_type(svinfo.get("group", "default"))
    }

    client_url = "http://localhost:{0}/url/register".format(
        CONFIG["proxy_client_port"])
    logger.info("Registering service {} to client at {} with params: {}".format(
        service_name, client_url, p))
    try:
        response = requests.post(
            client_url,
            json=p)
        logger.info("Service {} registered to client at {}".format(
            service_name, client_url))
        logger.info(
            "Response: {} - {}".format(response.status_code, response.text))
        resp_json = response.json()
        CONFIG["services"][service_name]["proxy_url_path"] = resp_json.get(
            "url_path", "")
    except requests.exceptions.RequestException as e:
        logger.error("Failed to register service {} to client: {}".format(
            service_name, str(e)))

def get_tunnel_type(group):

    group = group.lower()
    if "ai" in group:
        return "AI"
    elif "alg" in group:
        return "ALG"
    elif "api" in group:
        return "API"
    else:
        return "ALG"