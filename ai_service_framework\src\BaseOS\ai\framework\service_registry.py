
class ServiceRegistry:
    def __init__(self):
        self.registry = dict()

    def register_module(self, name):

        def _register(cls):
            assert (name not in self.registry), "{}已经被注册".format(name)
            self.registry[name] = cls
            return cls

        return _register

    def __getitem__(self, name):
        if name not in self.registry:
            registered_names = ', '.join([name for name in self.registry])
            raise KeyError("{}尚未被注册。所有被注册的服务名为:{}".format(
                name, registered_names))
        return self.registry[name]
