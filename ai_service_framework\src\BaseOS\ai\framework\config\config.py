import logging

# 全局注册信息（可按需扩展）
CONFIG = {
    "app_name": None,
    "app_port": None,
    "proxy_client_name": "MGS53",
    "proxy_client_group": "MGS",
    "proxy_client_port": None,
    "proxy_server_ip": "*************",
    "proxy_server_port": 8090,
    "agent_register_url": None,
    "agent_heartbeat_url": None,
    "baseos_node_id": None,
    "services": {}
}


logger = logging.getLogger("register_utils")


def reset_config():
    """
    重置全局注册信息
    """
    CONFIG["app_name"] = ""
    CONFIG["app_port"] = 5000
    CONFIG["proxy_client_name"] = "DefaultClient"
    CONFIG["proxy_client_group"] = "MGS"
    CONFIG["proxy_client_name"] = "MGS53"
    CONFIG["proxy_client_port"] = 8090
    CONFIG["proxy_server_ip"] = "127.0.0.1"
    CONFIG["proxy_server_port"] = 8090
    CONFIG["services"] = {}

# todo


def init_urls():
    for _, svinfo in CONFIG["services"].items():
        svinfo["url"] = "/{0}{1}".format(svinfo["name"],
                                         svinfo.get("url", "/predict"))


def set_register_config(config: dict):
    """
    用于初始化全局注册信息
    """
    CONFIG["app_name"] = config["app"].get("name")
    CONFIG["app_port"] = config["app"].get("port")
    CONFIG["proxy_client_name"] = config.get("register", {}).get("proxy_client_name")
    CONFIG["proxy_client_group"] = config.get("register", {}).get(
        "proxy_client_group", "MGS")
    CONFIG["proxy_client_port"] = config.get("register", {}).get(
        "proxy_client_port", 8090)
    CONFIG["proxy_server_ip"] = config.get("register", {}).get(
        "proxy_server_ip", "*************")
    CONFIG["proxy_server_port"] = config.get("register", {}).get(
        "proxy_server_port", 8090)
    CONFIG["agent_register_url"] = config.get("register", {}).get("agent_register_url")
    CONFIG["agent_heartbeat_url"] = config.get("register", {}).get(
        "agent_heartbeat_url")
    CONFIG["services"] = config.get("services")

    # TODO  initservices
