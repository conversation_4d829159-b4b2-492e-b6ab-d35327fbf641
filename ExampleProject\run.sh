#!/bin/bash

WORK_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/." && pwd)"
CONFIG_FILE="$WORK_DIR/Examples/config.json"

# Function to set environment variables
set_environment_variable() {
    local env_name="$1"
    local env_value="$2"

    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
        # Windows environment
        existing_value=$(powershell -Command "[Environment]::GetEnvironmentVariable('$env_name', 'Machine')")
        if [ ! -z "$existing_value" ]; then
            echo "$env_name already set to $existing_value, skipping overwrite."
            return
        fi
        powershell -Command "[Environment]::SetEnvironmentVariable('$env_name', '$env_value', 'Machine')"
    else
        # Linux environment
        local env_path="/BaseOS/.env"
        local content="${env_name}=\"${env_value}\""

        # 写入系统环境变量
        export "${env_name}=${env_value}"

        if [ ! -f "$env_path" ]; then
            echo "$content" > "$env_path"
        elif grep -q "^${env_name}=" "$env_path"; then
            echo "$env_name already set in $env_path, skipping overwrite."
        else
            echo "$content" >> "$env_path"
        fi
    fi
}

get_ipv4_address() {
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
        # Windows（Git Bash 或 WSL）
        ipconfig | awk '/IPv4 Address/ {gsub(/\r/, "", $NF); print $NF; exit}'
    elif command -v ip >/dev/null; then
        # Linux
        ip -4 route get ******* 2>/dev/null | awk '{print $7; exit}'
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        ipconfig getifaddr en0 2>/dev/null || ipconfig getifaddr en1 2>/dev/null
    elif command -v ifconfig >/dev/null; then
        # Fallback for older Linux/mac
        ifconfig | awk '/inet / && $2 != "127.0.0.1" {print $2; exit}'
    else
        echo "Unable to determine IP address"
    fi
}


check_env_vars() {
    # 检查操作系统类型
    OS_TYPE="$(uname | tr '[:upper:]' '[:lower:]')"
    local missing=0

    # 检查 HOST_IP
    if [ -z "$HOST_IP" ]; then
        echo "HOST_IP 环境变量未设置"
        missing=1
    else
        echo "HOST_IP=$HOST_IP"
    fi

    # 检查 SERVER_PORT
    if [ -z "$SERVER_PORT" ]; then
        echo "SERVER_PORT 环境变量未设置"
        missing=1
    else
        echo "SERVER_PORT=$SERVER_PORT"
    fi

    # 如果有变量未设置，说明是本地部署方式，从配置文件中获取
    if [ $missing -eq 1 ]; then
        APP_PORT=$(grep '"port"' "$CONFIG_FILE" | head -1 | sed 's/.*: *\([0-9]*\),*/\1/')
        set_environment_variable "HOST_IP" $(get_ipv4_address)
        set_environment_variable "SERVER_PORT" "$APP_PORT"
    fi
}


### strat ###

cd $WORK_DIR \
&& check_env_vars \
&& pip install *.whl \
&& cd ./Examples \
&& source start.sh