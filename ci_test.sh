#!/bin/bash

# CI Test Script
# This script runs the Flask application in the background and tests the JsonService endpoint

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
SERVER_URL="http://localhost:5000/JsonService/customize"
MAX_WAIT_TIME=30  # Maximum time to wait for server startup (seconds)
TEST_JSON='{"message": "Hello, World!", "timestamp": "2024-01-01T12:00:00Z", "data": {"key1": "value1", "key2": "value2"}}'

# Function to print colored output
print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if server is running
check_server() {
    curl -s -f "$SERVER_URL" > /dev/null 2>&1
    return $?
}

# Function to cleanup background processes
cleanup() {
    print_info "Cleaning up background processes..."
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
        print_info "Server process (PID: $SERVER_PID) terminated"
    fi
}

# Set up trap to cleanup on exit
trap cleanup EXIT

# Main execution
main() {
    print_info "Starting CI test for JsonService..."
    
    # Check if run.sh exists
    if [ ! -f "ExampleProject/run.sh" ]; then
        print_error "run.sh not found in ExampleProject directory"
        exit 1
    fi
    
    # Make run.sh executable
    chmod +x ExampleProject/run.sh
    
    # Start the server in background
    print_info "Starting server in background..."
    cd ExampleProject
    ./run.sh > ../server.log 2>&1 &
    SERVER_PID=$!
    cd ..
    
    print_info "Server started with PID: $SERVER_PID"
    print_info "Server logs are being written to server.log"
    
    # Wait for server to start
    print_info "Waiting for server to start (max ${MAX_WAIT_TIME}s)..."
    wait_time=0
    while [ $wait_time -lt $MAX_WAIT_TIME ]; do
        if check_server; then
            print_success "Server is ready!"
            break
        fi
        sleep 1
        wait_time=$((wait_time + 1))
        echo -n "."
    done
    echo
    
    if [ $wait_time -ge $MAX_WAIT_TIME ]; then
        print_error "Server failed to start within ${MAX_WAIT_TIME} seconds"
        print_info "Server log contents:"
        cat server.log
        exit 1
    fi
    
    # Send test request
    print_info "Sending POST request to $SERVER_URL"
    print_info "Request body: $TEST_JSON"
    
    response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -H "Content-Type: application/json" \
        -X POST \
        -d "$TEST_JSON" \
        "$SERVER_URL")
    
    # Extract HTTP status and response body
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    print_info "HTTP Status: $http_status"
    print_info "Response body: $response_body"
    
    # Check if request was successful
    if [ "$http_status" = "200" ]; then
        print_success "Test passed! Server responded with status 200"
        
        # Validate that response contains expected data
        if echo "$response_body" | grep -q "Hello, World!"; then
            print_success "Response contains expected data"
        else
            print_error "Response does not contain expected data"
            exit 1
        fi
    else
        print_error "Test failed! Server responded with status $http_status"
        print_error "Response: $response_body"
        exit 1
    fi
    
    print_success "All tests passed successfully!"
}

# Run main function
main "$@"
