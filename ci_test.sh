#!/bin/bash

# CI Test Script
# This script runs the Flask application in the background and tests the JsonService endpoint

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
SERVER_URL="http://localhost:5000/JsonService/customize"
MAX_WAIT_TIME=30  # Maximum time to wait for server startup (seconds)

# Test cases - different JSON payloads to test
declare -a TEST_CASES=(
    '{"simple": "test"}'
)

# Function to print colored output
print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if server is running by making actual requests
check_server() {
    local test_json='{"test": "server_check"}'
    local attempts=5
    local delay=3

    print_info "Checking server availability with $attempts requests (${delay}s interval)..."

    for i in $(seq 1 $attempts); do
        print_info "Attempt $i/$attempts..."

        # Send a test request to the actual service endpoint
        response=$(curl -s -w "HTTP_STATUS:%{http_code}" \
            -H "Content-Type: application/json" \
            -X POST \
            -d "$test_json" \
            "$SERVER_URL" 2>/dev/null)

        # Extract HTTP status
        http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)

        if [ "$http_status" = "200" ]; then
            print_success "Server is responding correctly (HTTP 200)"
            return 0
        fi

        if [ $i -lt $attempts ]; then
            print_info "Request failed (HTTP: $http_status), waiting ${delay}s before next attempt..."
            sleep $delay
        fi
    done

    print_error "Server failed to respond correctly after $attempts attempts"
    return 1
}

# Function to normalize JSON (remove whitespace)
normalize_json() {
    local json_string="$1"
    # Try using jq if available
    if command -v jq >/dev/null 2>&1; then
        echo "$json_string" | jq -c '.'
    else
        # Fallback: simple whitespace removal (not perfect but works for basic cases)
        echo "$json_string" | tr -d ' \t\n\r'
    fi
}

# Function to test a single JSON payload
test_json_payload() {
    local test_json="$1"
    local test_name="$2"

    print_info "Testing case: $test_name"
    print_info "Request body: $test_json"

    response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -H "Content-Type: application/json" \
        -X POST \
        -d "$test_json" \
        "$SERVER_URL")

    # Extract HTTP status and response body
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')

    print_info "HTTP Status: $http_status"
    print_info "Response body: $response_body"

    # Check if request was successful
    if [ "$http_status" = "200" ]; then
        # Validate that response body is identical to request body
        normalized_request=$(normalize_json "$test_json")
        normalized_response=$(normalize_json "$response_body")

        if [ "$normalized_request" = "$normalized_response" ]; then
            print_success "✓ $test_name - Response matches request exactly!"
            return 0
        else
            print_error "✗ $test_name - Response does not match request!"
            print_error "Expected: $normalized_request"
            print_error "Received: $normalized_response"
            return 1
        fi
    else
        print_error "✗ $test_name - Server responded with status $http_status"
        print_error "Response: $response_body"
        return 1
    fi
}

# Function to cleanup background processes
cleanup() {
    print_info "Cleaning up background processes..."
    # 调用本地 8090 注销接口
    for base_url in "/" "/JsonService/customize"; do
        print_info "Unregistering url_path=/Tunnel/API/MGS/CI/CI-Client, base_url=$base_url ..."
        curl -s -X POST "http://localhost:8090/url/unregister" \
            -H "Content-Type: application/json" \
            -d "{\"url_path\":\"/Tunnel/API/MGS/CI/CI-Client\",\"base_url\":\"$base_url\"}"
    done
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
        print_info "Server process (PID: $SERVER_PID) terminated"
    fi
}

# Set up trap to cleanup on exit
trap cleanup EXIT

# Main execution
main() {
    print_info "Starting CI test for JsonService..."
    
    # Check if run.sh exists
    if [ ! -f "ExampleProject/run.sh" ]; then
        print_error "run.sh not found in ExampleProject directory"
        exit 1
    fi
    
    # Make run.sh executable
    chmod +x ExampleProject/run.sh
    
    # Start the server in background
    print_info "Starting server in background..."
    cd ExampleProject
    bash run.sh > ../server.log 2>&1 &
    SERVER_PID=$!
    cd ..
    
    print_info "Server started with PID: $SERVER_PID"
    print_info "Server logs are being written to server.log"
    
    # Wait a moment for server to initialize, then check if it's ready
    print_info "Waiting for server to initialize..."
    sleep 3

    if ! check_server; then
        print_error "Server failed to start properly"
        print_info "Server log contents:"
        cat server.log
        exit 1
    fi
    
    # Run all test cases
    print_info "Running JSON payload tests..."
    failed_tests=0
    total_tests=${#TEST_CASES[@]}

    for i in "${!TEST_CASES[@]}"; do
        test_case="${TEST_CASES[$i]}"
        test_name="Test Case $((i+1))/$total_tests"

        echo
        if ! test_json_payload "$test_case" "$test_name"; then
            failed_tests=$((failed_tests + 1))
        fi
    done

    echo
    print_info "Test Summary:"
    print_info "Total tests: $total_tests"
    print_info "Passed: $((total_tests - failed_tests))"
    print_info "Failed: $failed_tests"

    if [ $failed_tests -eq 0 ]; then
        print_success "All tests passed successfully! 🎉"
        print_success "JsonService correctly returns identical JSON for all test cases."
    else
        print_error "Some tests failed! ❌"
        exit 1
    fi
}

# Run main function
main "$@"
