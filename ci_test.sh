#!/bin/bash

# CI Test Script
# This script runs the Flask application in the background and tests the JsonService endpoint

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
SERVER_URL="http://localhost:5000/JsonService/customize"
MAX_WAIT_TIME=30  # Maximum time to wait for server startup (seconds)

# Test cases - different JSON payloads to test
declare -a TEST_CASES=(
    '{"message": "Hello, World!", "timestamp": "2024-01-01T12:00:00Z", "data": {"key1": "value1", "key2": "value2"}}'
    '{"simple": "test"}'
    '{"array": [1, 2, 3], "boolean": true, "null_value": null}'
    '{"nested": {"deep": {"value": "test"}, "numbers": [1.5, -2, 0]}}'
)

# Function to print colored output
print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if server is running
check_server() {
    curl -s -f "$SERVER_URL" > /dev/null 2>&1
    return $?
}

# Function to normalize JSON (remove whitespace)
normalize_json() {
    local json_string="$1"
    # Try using jq if available
    if command -v jq >/dev/null 2>&1; then
        echo "$json_string" | jq -c '.'
    else
        # Fallback: simple whitespace removal (not perfect but works for basic cases)
        echo "$json_string" | tr -d ' \t\n\r'
    fi
}

# Function to test a single JSON payload
test_json_payload() {
    local test_json="$1"
    local test_name="$2"

    print_info "Testing case: $test_name"
    print_info "Request body: $test_json"

    response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -H "Content-Type: application/json" \
        -X POST \
        -d "$test_json" \
        "$SERVER_URL")

    # Extract HTTP status and response body
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')

    print_info "HTTP Status: $http_status"
    print_info "Response body: $response_body"

    # Check if request was successful
    if [ "$http_status" = "200" ]; then
        # Validate that response body is identical to request body
        normalized_request=$(normalize_json "$test_json")
        normalized_response=$(normalize_json "$response_body")

        if [ "$normalized_request" = "$normalized_response" ]; then
            print_success "✓ $test_name - Response matches request exactly!"
            return 0
        else
            print_error "✗ $test_name - Response does not match request!"
            print_error "Expected: $normalized_request"
            print_error "Received: $normalized_response"
            return 1
        fi
    else
        print_error "✗ $test_name - Server responded with status $http_status"
        print_error "Response: $response_body"
        return 1
    fi
}

# Function to cleanup background processes
cleanup() {
    print_info "Cleaning up background processes..."
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
        print_info "Server process (PID: $SERVER_PID) terminated"
    fi
}

# Set up trap to cleanup on exit
trap cleanup EXIT

# Main execution
main() {
    print_info "Starting CI test for JsonService..."
    
    # Check if run.sh exists
    if [ ! -f "ExampleProject/run.sh" ]; then
        print_error "run.sh not found in ExampleProject directory"
        exit 1
    fi
    
    # Make run.sh executable
    chmod +x ExampleProject/run.sh
    
    # Start the server in background
    print_info "Starting server in background..."
    cd ExampleProject
    ./run.sh > ../server.log 2>&1 &
    SERVER_PID=$!
    cd ..
    
    print_info "Server started with PID: $SERVER_PID"
    print_info "Server logs are being written to server.log"
    
    # Wait for server to start
    print_info "Waiting for server to start (max ${MAX_WAIT_TIME}s)..."
    wait_time=0
    while [ $wait_time -lt $MAX_WAIT_TIME ]; do
        if check_server; then
            print_success "Server is ready!"
            break
        fi
        sleep 1
        wait_time=$((wait_time + 1))
        echo -n "."
    done
    echo
    
    if [ $wait_time -ge $MAX_WAIT_TIME ]; then
        print_error "Server failed to start within ${MAX_WAIT_TIME} seconds"
        print_info "Server log contents:"
        cat server.log
        exit 1
    fi
    
    # Run all test cases
    print_info "Running JSON payload tests..."
    failed_tests=0
    total_tests=${#TEST_CASES[@]}

    for i in "${!TEST_CASES[@]}"; do
        test_case="${TEST_CASES[$i]}"
        test_name="Test Case $((i+1))/$total_tests"

        echo
        if ! test_json_payload "$test_case" "$test_name"; then
            failed_tests=$((failed_tests + 1))
        fi
    done

    echo
    print_info "Test Summary:"
    print_info "Total tests: $total_tests"
    print_info "Passed: $((total_tests - failed_tests))"
    print_info "Failed: $failed_tests"

    if [ $failed_tests -eq 0 ]; then
        print_success "All tests passed successfully! 🎉"
        print_success "JsonService correctly returns identical JSON for all test cases."
    else
        print_error "Some tests failed! ❌"
        exit 1
    fi
}

# Run main function
main "$@"
